{"rustc": 12848521558442915983, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 16690480377348987070, "path": 3023614298992950866, "deps": [[2964536209444415731, "memchr", false, 12298372592010482392], [6314779025451150414, "regex_automata", false, 8967648590980187952], [7325384046744447800, "aho_corasick", false, 8091646628650326030], [9111760993595911334, "regex_syntax", false, 1029509166089000748]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-5afa7447b0933ba3/dep-lib-regex"}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}