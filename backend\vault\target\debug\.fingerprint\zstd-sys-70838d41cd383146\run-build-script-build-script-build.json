{"rustc": 12848521558442915983, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3620218005424189895, "build_script_build", false, 2375566857864940760]], "local": [{"RerunIfEnvChanged": {"var": "ZSTD_SYS_USE_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-unknown-linux-musl", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_unknown_linux_musl", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-unknown-linux-musl", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}