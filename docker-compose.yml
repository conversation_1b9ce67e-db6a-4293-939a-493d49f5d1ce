version: '3.8'

services:
  # Frontend Next.js
  frontend:
    build:
      context: ./frontend
      dockerfile: web/Dockerfile
      target: development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/web:/app/web
      - ./frontend/packages:/app/packages
      - /app/web/node_modules
      - /app/packages/config/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
    networks:
      - finshark-network
    depends_on:
      - backend-users
      - backend-auth
      - backend-vault
      - backend-notif

  # Backend NestJS (Users)
  backend-users:
    build:
      context: ./backend/users
      dockerfile: Dockerfile
      target: development
    ports:
      - "3001:3001"
    volumes:
      - ./backend/users:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=********************************************/finshark_users
    networks:
      - finshark-network
    depends_on:
      - postgres

  # Backend Rust (Vault)
  backend-vault:
    build:
      context: ./backend/vault
      dockerfile: Dockerfile
      target: development
    ports:
      - "8080:8080"
    volumes:
      - ./backend/vault:/app
    environment:
      - RUST_LOG=debug
      - RUST_BACKTRACE=1
    networks:
      - finshark-network

  # Backend Go (Auth)
  backend-auth:
    build:
      context: ./backend/auth
      dockerfile: Dockerfile
      target: development
    ports:
      - "8003:8003"
    volumes:
      - ./backend/auth:/app
    environment:
      - GIN_MODE=debug
      - DATABASE_URL=********************************************/finshark_auth
    networks:
      - finshark-network
    depends_on:
      - postgres
      - redis

  # Backend Python (Notifications)
  backend-notif:
    build:
      context: ./backend/notif
      dockerfile: Dockerfile
      target: development
    ports:
      - "8002:8002"
    volumes:
      - ./backend/notif:/app
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=********************************************/finshark_notif
      - REDIS_URL=redis://redis:6379
    networks:
      - finshark-network
    depends_on:
      - postgres
      - redis

  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=finshark
      - POSTGRES_PASSWORD=FinShark2025!
      - POSTGRES_DB=finshark
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - finshark-network

  # Redis pour le cache et les sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - finshark-network

  # Nginx reverse proxy (optionnel)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - finshark-network
    depends_on:
      - frontend
      - backend-users
      - backend-auth
      - backend-vault
      - backend-notif

volumes:
  postgres_data:
  redis_data:

networks:
  finshark-network:
    driver: bridge
