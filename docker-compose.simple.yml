version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=finshark
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=finshark
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - finshark-network

  # Redis pour le cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - finshark-network

  # Frontend Next.js (test simple)
  frontend:
    image: node:20-alpine
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/web:/app
    command: sh -c "npm install && npm run dev"
    environment:
      - NODE_ENV=development
    networks:
      - finshark-network
    depends_on:
      - postgres
      - redis

volumes:
  postgres_data:
  redis_data:

networks:
  finshark-network:
    driver: bridge
