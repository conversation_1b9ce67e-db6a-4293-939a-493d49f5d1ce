# Dockerfile pour le service Python (Notifications)
FROM python:3.11-alpine AS base

# Installer les dépendances système
RUN apk add --no-cache \
    gcc \
    musl-dev \
    libffi-dev \
    openssl-dev

WORKDIR /app

# Copier les fichiers de requirements
COPY requirements.txt ./

# Installer les dépendances Python
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# Stage de développement
FROM base AS development
COPY . .
EXPOSE 8002
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8002", "--reload"]

# Stage de production
FROM python:3.11-alpine AS production

# Installer les dépendances runtime
RUN apk add --no-cache libffi openssl

# Créer un utilisateur non-root
RUN addgroup -g 1001 -S python
RUN adduser -S python -u 1001

WORKDIR /app

# Copier les dépendances depuis le stage de base
COPY --from=base /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=base /usr/local/bin /usr/local/bin

# Copier le code source
COPY --chown=python:python . .

USER python

EXPOSE 8002

# Variables d'environnement
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8002/')" || exit 1

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8002"]
