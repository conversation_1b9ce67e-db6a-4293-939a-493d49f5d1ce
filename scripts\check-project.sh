#!/bin/bash

# Script de vérification complète du projet FinShark

echo "🔍 Vérification complète du projet FinShark..."
echo "================================================"

# Fonction pour vérifier si un fichier existe
check_file() {
    if [ -f "$1" ]; then
        echo "✅ $1"
    else
        echo "❌ $1 - MANQUANT"
    fi
}

# Fonction pour vérifier si un dossier existe
check_dir() {
    if [ -d "$1" ]; then
        echo "✅ $1/"
    else
        echo "❌ $1/ - MANQUANT"
    fi
}

echo ""
echo "📁 Structure du projet:"
check_dir "frontend"
check_dir "frontend/web"
check_dir "frontend/mobile"
check_dir "frontend/packages/config"
check_dir "backend"
check_dir "backend/users"
check_dir "backend/auth"
check_dir "backend/vault"
check_dir "backend/notif"
check_dir "docker"
check_dir "scripts"

echo ""
echo "📄 Fichiers de configuration principaux:"
check_file "package.json"
check_file "docker-compose.yml"
check_file "docker-compose.prod.yml"
check_file ".env.example"
check_file ".dockerignore"

echo ""
echo "🐳 Configuration Docker:"
check_file "backend/users/Dockerfile"
check_file "backend/auth/Dockerfile"
check_file "backend/vault/Dockerfile"
check_file "backend/notif/Dockerfile"
check_file "frontend/web/Dockerfile"
check_file "docker/postgres/init.sql"
check_file "docker/nginx/nginx.conf"

echo ""
echo "🔧 Services Backend:"
check_file "backend/users/package.json"
check_file "backend/users/src/main.ts"
check_file "backend/auth/main.go"
check_file "backend/auth/go.mod"
check_file "backend/vault/Cargo.toml"
check_file "backend/vault/src/main.rs"
check_file "backend/notif/main.py"
check_file "backend/notif/requirements.txt"

echo ""
echo "🌐 Frontend:"
check_file "frontend/web/package.json"
check_file "frontend/web/next.config.ts"
check_file "frontend/mobile/package.json"
check_file "frontend/packages/config/package.json"

echo ""
echo "⚙️ Configuration centralisée:"
check_file "frontend/packages/config/eslint.config.js"
check_file "frontend/packages/config/prettier.config.js"
check_file "frontend/packages/config/tailwind.config.js"
check_file "frontend/packages/config/tsconfig.base.json"
check_file "frontend/packages/config/postcss.config.js"

echo ""
echo "📊 Résumé de l'état du projet:"
echo "================================"

# Vérifier si Docker est installé
if command -v docker &> /dev/null; then
    echo "✅ Docker installé"
    if command -v docker-compose &> /dev/null; then
        echo "✅ Docker Compose installé"
    else
        echo "❌ Docker Compose non installé"
    fi
else
    echo "❌ Docker non installé"
fi

# Vérifier si Node.js est installé
if command -v node &> /dev/null; then
    echo "✅ Node.js installé ($(node --version))"
else
    echo "❌ Node.js non installé"
fi

# Vérifier si Rust est installé
if command -v cargo &> /dev/null; then
    echo "✅ Rust/Cargo installé ($(cargo --version))"
else
    echo "❌ Rust/Cargo non installé"
fi

# Vérifier si Go est installé
if command -v go &> /dev/null; then
    echo "✅ Go installé ($(go version))"
else
    echo "❌ Go non installé"
fi

# Vérifier si Python est installé
if command -v python &> /dev/null; then
    echo "✅ Python installé ($(python --version))"
elif command -v python3 &> /dev/null; then
    echo "✅ Python installé ($(python3 --version))"
else
    echo "❌ Python non installé"
fi

echo ""
echo "🚀 Commandes pour démarrer:"
echo "============================"
echo "Développement local:     npm run dev"
echo "Docker développement:    npm run docker:dev"
echo "Docker production:       npm run docker:prod"
echo ""
echo "📝 Pour plus d'informations, consultez:"
echo "- docker/README.md"
echo "- frontend/packages/config/README.md"
