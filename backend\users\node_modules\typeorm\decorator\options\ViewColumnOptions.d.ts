import { ValueTransformer } from "./ValueTransformer";
/**
 * Describes all view column's options.
 */
export interface ViewColumnOptions {
    /**
     * Column name in the database.
     */
    name?: string;
    /**
     * Specifies a value transformer(s) that is to be used to unmarshal
     * this column when reading from the database.
     */
    transformer?: ValueTransformer | ValueTransformer[];
}
