@import 'tailwindcss';

/* Variables CSS personnalisées pour FinShark */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
  --secondary: #22c55e;
  --accent: #eab308;
  --muted: #f5f5f5;
  --border: #e5e5e5;
  --radius: 0.5rem;
}

/* Configuration du thème Tailwind v4 */
@theme inline {
  /* Couleurs personnalisées */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);

  /* Typographie */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;

  /* Rayons de bordure */
  --radius: var(--radius);
  --radius-sm: calc(var(--radius) - 2px);
  --radius-lg: calc(var(--radius) + 2px);

  /* Couleurs FinShark étendues */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-900: #1e3a8a;

  --color-secondary-50: #f0fdf4;
  --color-secondary-100: #dcfce7;
  --color-secondary-500: #22c55e;
  --color-secondary-600: #16a34a;
  --color-secondary-900: #14532d;

  --color-accent-50: #fefce8;
  --color-accent-100: #fef9c3;
  --color-accent-500: #eab308;
  --color-accent-600: #ca8a04;
  --color-accent-900: #713f12;
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --muted: #262626;
    --border: #404040;
  }
}

/* Styles de base */
* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Classes utilitaires personnalisées */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .bg-gradient-finshark {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  }

  .shadow-finshark {
    box-shadow: 0 4px 6px -1px rgb(59 130 246 / 0.1), 0 2px 4px -1px rgb(59 130 246 / 0.06);
  }
}
