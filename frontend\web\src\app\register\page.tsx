'use client';

import  { useState } from 'react';
import { useRouter } from 'next/navigation';
import { RegisterForm } from '../../components/auth/RegisterForm';
import { CheckCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function RegisterPage() {
  const router = useRouter();
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [registeredUser, setRegisteredUser] = useState<any>(null);

  const handleRegistrationSuccess = (user: any) => {
    setRegisteredUser(user);
    setRegistrationSuccess(true);
    
    // Rediriger vers la page de connexion après 3 secondes
    setTimeout(() => {
      router.push('/login');
    }, 3000);
  };

  const handleRegistrationError = (error: string) => {
    console.error('Erreur d\'inscription:', error);
    // L'erreur est déjà gérée dans le composant RegisterForm
  };

  if (registrationSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-emerald-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100 text-center">
            {/* Icône de succès */}
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>

            {/* Message de succès */}
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Inscription réussie ! 🎉
            </h2>
            
            <p className="text-gray-600 mb-6">
              Bienvenue <span className="font-semibold text-emerald-600">{registeredUser?.username}</span> !<br />
              Votre compte FinShark a été créé avec succès.
            </p>

            {/* Informations du compte */}
            <div className="bg-emerald-50 rounded-lg p-4 mb-6">
              <div className="text-sm text-emerald-800">
                <p><strong>Nom d'utilisateur :</strong> {registeredUser?.username}</p>
                <p><strong>ID :</strong> {registeredUser?.id}</p>
                <p><strong>Créé le :</strong> {new Date(registeredUser?.created_at).toLocaleDateString('fr-FR')}</p>
              </div>
            </div>

            {/* Message de redirection */}
            <p className="text-sm text-gray-500 mb-6">
              Vous allez être redirigé vers la page de connexion dans quelques secondes...
            </p>

            {/* Boutons d'action */}
            <div className="space-y-3">
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-gradient-to-r from-emerald-500 to-emerald-600 text-white py-3 px-4 rounded-lg font-medium hover:from-emerald-600 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200"
              >
                Se connecter maintenant
              </button>
              
              <button
                onClick={() => router.push('/')}
                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
              >
                Retour à l'accueil
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-emerald-50">
      {/* Header avec navigation */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo et titre */}
            <div className="flex items-center gap-3">
              <Link
                href="/"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="text-sm font-medium">Retour</span>
              </Link>
            </div>

            {/* Navigation */}
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">Déjà un compte ?</span>
              <Link
                href="/login"
                className="text-emerald-600 hover:text-emerald-700 font-medium text-sm transition-colors"
              >
                Se connecter
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Contenu principal */}
      <main className="flex items-center justify-center min-h-[calc(100vh-4rem)] p-4">
        <div className="w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          {/* Section gauche - Informations */}
          <div className="hidden lg:block">
            <div className="max-w-lg">
              <h1 className="text-4xl font-bold text-gray-900 mb-6">
                Rejoignez la communauté
                <span className="text-emerald-600"> FinShark</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8">
                Créez votre compte et commencez votre aventure financière avec des outils puissants et une interface intuitive.
              </p>

              {/* Avantages */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-emerald-600" />
                  </div>
                  <span className="text-gray-700">Gestion de portefeuille avancée</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-emerald-600" />
                  </div>
                  <span className="text-gray-700">Analyses et rapports détaillés</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-emerald-600" />
                  </div>
                  <span className="text-gray-700">Notifications intelligentes</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-emerald-600" />
                  </div>
                  <span className="text-gray-700">Sécurité de niveau bancaire</span>
                </div>
              </div>

              {/* Statistiques */}
              <div className="mt-12 grid grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-600">10K+</div>
                  <div className="text-sm text-gray-600">Utilisateurs actifs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-600">€2M+</div>
                  <div className="text-sm text-gray-600">Actifs gérés</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-600">99.9%</div>
                  <div className="text-sm text-gray-600">Disponibilité</div>
                </div>
              </div>
            </div>
          </div>

          {/* Section droite - Formulaire */}
          <div className="w-full">
            <RegisterForm 
              onSuccess={handleRegistrationSuccess}
              onError={handleRegistrationError}
            />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-sm text-gray-600">
            <p>
              En créant un compte, vous acceptez nos{' '}
              <Link href="/terms" className="text-emerald-600 hover:text-emerald-700">
                Conditions d'utilisation
              </Link>{' '}
              et notre{' '}
              <Link href="/privacy" className="text-emerald-600 hover:text-emerald-700">
                Politique de confidentialité
              </Link>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
