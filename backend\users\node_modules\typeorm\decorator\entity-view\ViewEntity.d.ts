import { ViewEntityOptions } from "../options/ViewEntityOptions";
/**
 * This decorator is used to mark classes that will be an entity view.
 * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.
 */
export declare function ViewEntity(options?: ViewEntityOptions): ClassDecorator;
/**
 * This decorator is used to mark classes that will be an entity view.
 * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.
 */
export declare function ViewEntity(name?: string, options?: ViewEntityOptions): ClassDecorator;
