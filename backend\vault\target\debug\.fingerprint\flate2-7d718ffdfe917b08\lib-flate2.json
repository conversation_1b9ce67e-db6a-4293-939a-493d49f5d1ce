{"rustc": 12848521558442915983, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 10676753825826352750, "profile": 16690480377348987070, "path": 13390239866406959583, "deps": [[2989785442755699193, "crc32fast", false, 12650323034551020078], [12522475594372247311, "miniz_oxide", false, 530930548752541979]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-7d718ffdfe917b08/dep-lib-flate2"}}], "rustflags": [], "metadata": 1284714256429684901, "config": 2202906307356721367, "compile_kind": 0}