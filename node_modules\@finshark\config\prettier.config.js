// Configuration Prettier partagée pour tous les projets FinShark
/** @type {import('prettier').Config} */
export default {
  // Formatage de base
  printWidth: 80,
  tabWidth: 2,
  useTabs: false,
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',

  // JSX
  jsxSingleQuote: true,

  // Virgules
  trailingComma: 'es5',

  // Espaces
  bracketSpacing: true,
  bracketSameLine: false,

  // Fonctions fléchées
  arrowParens: 'avoid',

  // Fin de ligne
  endOfLine: 'lf',

  // Formatage conditionnel
  embeddedLanguageFormatting: 'auto',

  // Plugins spécifiques
  plugins: [],

  // Surcharges par type de fichier
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 100,
        proseWrap: 'always',
      },
    },
    {
      files: '*.{yml,yaml}',
      options: {
        tabWidth: 2,
      },
    },
  ],
};
