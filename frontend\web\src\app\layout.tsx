// src/app/layout.tsx
import { Playfair_Display, Inter, JetBrains_Mono } from 'next/font/google';
import './globals.css';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'FinShark – Gestion chic de vos comptes',
  description:
    'Ne laissez plus l\'argent vous filer entre les doigts. Suivez vos dépenses, optimisez votre budget et anticipez vos besoins.',
};

const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-display',
});
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-body',
});
const jetbrains = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html
      lang="fr"
      className={`${playfair.variable} ${inter.variable} ${jetbrains.variable}`}
    >
      <body className="bg-shark-900 text-platinum-100 font-body antialiased">
        {children}
      </body>
    </html>
  );
}