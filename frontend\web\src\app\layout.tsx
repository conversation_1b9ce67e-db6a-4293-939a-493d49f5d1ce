import type { Metadata } from 'next';
import { Playfair_Display, Inter, JetBrains_Mono } from 'next/font/google';
import './globals.css';

// Configuration des polices Google Fonts pour FinShark
const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-display',
  display: 'swap',
});

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-body',
  display: 'swap',
});

const jetbrains = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'FinShark – Gestion chic de vos comptes',
  description: 'Ne laissez plus l\'argent vous filer entre les doigts. Suivez vos dépenses, optimisez votre budget et anticipez vos besoins.',
  keywords: ['finance', 'budget', 'gestion', 'comptes', 'dépenses'],
  authors: [{ name: 'FinShark Team' }],
  icons: [
    {
      url: '/favicon.svg',
      type: 'image/svg+xml',
    },
  ],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#10b981',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="fr"
      className={`${playfair.variable} ${inter.variable} ${jetbrains.variable}`}
    >
      <body className="bg-shark-900 text-platinum-100 font-body antialiased">
        {children}
      </body>
    </html>
  );
}
