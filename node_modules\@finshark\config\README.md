# @finshark/config

Configuration partagée pour tous les projets frontend FinShark.

## 📦 Contenu

Ce package contient les configurations standardisées pour :

- **ESLint** - Linting JavaScript/TypeScript
- **Prettier** - Formatage de code
- **PostCSS** - Traitement CSS
- **Tailwind CSS** - Framework CSS utilitaire
- **TypeScript** - Configuration TypeScript de base

## 🚀 Utilisation

### Installation

```bash
npm install @finshark/config --save-dev
```

### ESLint

#### Pour React/Next.js (Web)

```javascript
// eslint.config.mjs
import { reactConfig } from '@finshark/config/eslint';

export default reactConfig;
```

#### Pour React Native/Expo (Mobile)

```javascript
// eslint.config.js
import { reactNativeConfig } from '@finshark/config/eslint';

export default reactNativeConfig;
```

### Prettier

```javascript
// prettier.config.js
import config from '@finshark/config/prettier';

export default config;
```

### PostCSS

```javascript
// postcss.config.mjs
import config from '@finshark/config/postcss';

export default config;
```

### Tailwind CSS

```javascript
// tailwind.config.js
import baseConfig from '@finshark/config/tailwind';

export default {
  ...baseConfig,
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    // Ajoutez vos chemins spécifiques
  ],
};
```

### TypeScript

```json
// tsconfig.json
{
  "extends": "@finshark/config/tsconfig",
  "compilerOptions": {
    // Vos options spécifiques
  }
}
```

## 🎨 Thème de couleurs

Le thème Tailwind inclut les couleurs de la marque FinShark :

- **Primary** : Bleu (#3b82f6)
- **Secondary** : Vert (#22c55e)
- **Accent** : Jaune (#eab308)
- **Neutral** : Gris
- **Semantic** : Success, Warning, Error, Info

## 📝 Scripts disponibles

- `npm run lint` - Vérifier le code avec ESLint
- `npm run lint:fix` - Corriger automatiquement les erreurs ESLint
- `npm run format` - Formater le code avec Prettier
- `npm run format:check` - Vérifier le formatage
- `npm run type-check` - Vérifier les types TypeScript

## 🔧 Personnalisation

Chaque configuration peut être étendue dans vos projets spécifiques. Consultez la documentation de
chaque outil pour plus de détails.

## 📄 Licence

MIT
