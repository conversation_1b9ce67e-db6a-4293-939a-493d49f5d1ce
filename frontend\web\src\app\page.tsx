import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-shark-900 via-shark-800 to-shark-900">
      {/* Header */}
      <header className="container mx-auto px-6 py-8">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">F</span>
            </div>
            <h1 className="text-2xl font-display font-bold text-platinum-100">
              FinShark
            </h1>
          </div>
          <div className="flex items-center space-x-4">
            <Link
              href="/login"
              className="text-platinum-300 hover:text-platinum-100 transition-colors"
            >
              Connexion
            </Link>
            <Link
              href="/register"
              className="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Commencer
            </Link>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-6 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-5xl md:text-6xl font-display font-bold text-platinum-100 mb-6">
            Gestion <span className="text-emerald-400">chic</span> de vos comptes
          </h2>
          <p className="text-xl text-platinum-300 mb-8 max-w-2xl mx-auto">
            Ne laissez plus l'argent vous filer entre les doigts.
            Suivez vos dépenses, optimisez votre budget et anticipez vos besoins.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register"
              className="bg-emerald-500 hover:bg-emerald-600 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors text-center"
            >
              Démarrer gratuitement
            </Link>
            <button className="border border-platinum-600 text-platinum-100 hover:bg-platinum-800 px-8 py-4 rounded-lg font-medium text-lg transition-colors">
              Voir la démo
            </button>
          </div>
        </div>

        {/* Features Preview */}
        <div className="mt-24 grid md:grid-cols-3 gap-8">
          <div className="bg-shark-800/50 backdrop-blur-sm border border-shark-700 rounded-xl p-6">
            <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center mb-4">
              <span className="text-emerald-400 text-2xl">📊</span>
            </div>
            <h3 className="text-xl font-semibold text-platinum-100 mb-2">
              Suivi en temps réel
            </h3>
            <p className="text-platinum-400">
              Visualisez vos finances instantanément avec des graphiques intuitifs et des alertes personnalisées.
            </p>
          </div>

          <div className="bg-shark-800/50 backdrop-blur-sm border border-shark-700 rounded-xl p-6">
            <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center mb-4">
              <span className="text-emerald-400 text-2xl">🎯</span>
            </div>
            <h3 className="text-xl font-semibold text-platinum-100 mb-2">
              Objectifs intelligents
            </h3>
            <p className="text-platinum-400">
              Définissez et atteignez vos objectifs financiers avec notre assistant IA personnalisé.
            </p>
          </div>

          <div className="bg-shark-800/50 backdrop-blur-sm border border-shark-700 rounded-xl p-6">
            <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center mb-4">
              <span className="text-emerald-400 text-2xl">🔒</span>
            </div>
            <h3 className="text-xl font-semibold text-platinum-100 mb-2">
              Sécurité maximale
            </h3>
            <p className="text-platinum-400">
              Vos données sont protégées par un chiffrement de niveau bancaire et une architecture sécurisée.
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-6 py-8 mt-16 border-t border-shark-700">
        <div className="text-center text-platinum-400">
          <p>&copy; 2024 FinShark. Tous droits réservés.</p>
        </div>
      </footer>
    </div>
  );
}
