{"version": 3, "file": "share.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/share.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAG/C,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAwIvC,MAAM,UAAU,KAAK,CAAI,UAA0B,EAAE;IACnD,MAAM,EAAE,SAAS,GAAG,GAAG,EAAE,CAAC,IAAI,OAAO,EAAK,EAAE,YAAY,GAAG,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,mBAAmB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IAUhI,OAAO,CAAC,aAAa,EAAE,EAAE;QACvB,IAAI,UAAyC,CAAC;QAC9C,IAAI,eAAyC,CAAC;QAC9C,IAAI,OAAmC,CAAC;QACxC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,WAAW,EAAE,CAAC;YAC/B,eAAe,GAAG,SAAS,CAAC;QAC9B,CAAC,CAAC;QAGF,MAAM,KAAK,GAAG,GAAG,EAAE;YACjB,WAAW,EAAE,CAAC;YACd,UAAU,GAAG,OAAO,GAAG,SAAS,CAAC;YACjC,YAAY,GAAG,UAAU,GAAG,KAAK,CAAC;QACpC,CAAC,CAAC;QACF,MAAM,mBAAmB,GAAG,GAAG,EAAE;YAG/B,MAAM,IAAI,GAAG,UAAU,CAAC;YACxB,KAAK,EAAE,CAAC;YACR,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,EAAE,CAAC;QACtB,CAAC,CAAC;QAEF,OAAO,OAAO,CAAO,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;YAC1C,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,EAAE;gBAChC,WAAW,EAAE,CAAC;aACf;YAMD,MAAM,IAAI,GAAG,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,SAAS,EAAE,CAAC,CAAC;YAOhD,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE;gBAClB,QAAQ,EAAE,CAAC;gBAKX,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,EAAE;oBAClD,eAAe,GAAG,WAAW,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;iBACzE;YACH,CAAC,CAAC,CAAC;YAIH,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE3B,IACE,CAAC,UAAU;gBAIX,QAAQ,GAAG,CAAC,EACZ;gBAMA,UAAU,GAAG,IAAI,cAAc,CAAC;oBAC9B,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBACjC,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE;wBACb,UAAU,GAAG,IAAI,CAAC;wBAClB,WAAW,EAAE,CAAC;wBACd,eAAe,GAAG,WAAW,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;wBACxD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAClB,CAAC;oBACD,QAAQ,EAAE,GAAG,EAAE;wBACb,YAAY,GAAG,IAAI,CAAC;wBACpB,WAAW,EAAE,CAAC;wBACd,eAAe,GAAG,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;wBACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,CAAC;iBACF,CAAC,CAAC;gBACH,SAAS,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACzC;QACH,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAClB,KAAiB,EACjB,EAAoD,EACpD,GAAG,IAAO;IAEV,IAAI,EAAE,KAAK,IAAI,EAAE;QACf,KAAK,EAAE,CAAC;QACR,OAAO;KACR;IAED,IAAI,EAAE,KAAK,KAAK,EAAE;QAChB,OAAO;KACR;IAED,MAAM,YAAY,GAAG,IAAI,cAAc,CAAC;QACtC,IAAI,EAAE,GAAG,EAAE;YACT,YAAY,CAAC,WAAW,EAAE,CAAC;YAC3B,KAAK,EAAE,CAAC;QACV,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;AACxD,CAAC"}