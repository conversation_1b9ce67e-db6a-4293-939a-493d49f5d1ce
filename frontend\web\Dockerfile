# Dockerfile pour le frontend Next.js
FROM node:20-alpine AS base

# Installer les dépendances système
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Stage des dépendances
FROM base AS deps

# Copier les fichiers de configuration
COPY package*.json ./
COPY ../packages/config/package*.json ../packages/config/

# Installer les dépendances
RUN npm ci

# Stage de développement
FROM base AS development
COPY --from=deps /app/node_modules ./node_modules
COPY . .
COPY ../packages/config ../packages/config

EXPOSE 3000

ENV NODE_ENV=development

CMD ["npm", "run", "dev"]

# Stage de build
FROM base AS build

COPY --from=deps /app/node_modules ./node_modules
COPY . .
COPY ../packages/config ../packages/config

# Variables d'environnement pour le build
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Build de l'application
RUN npm run build

# Stage de production
FROM node:20-alpine AS production

WORKDIR /app

# Créer un utilisateur non-root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copier les fichiers nécessaires
COPY --from=build /app/public ./public

# Copier les fichiers de build
COPY --from=build --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=build --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

# Variables d'environnement
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/ || exit 1

CMD ["node", "server.js"]
