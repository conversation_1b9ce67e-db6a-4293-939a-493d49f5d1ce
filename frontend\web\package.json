{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.4"}, "devDependencies": {"@finshark/config": "file:../packages/config", "typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.4", "@eslint/eslintrc": "^3", "prettier": "^3.6.2"}}