{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@swc/helpers": "^0.5.17", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "scheduler": "^0.26.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@finshark/config": "file:../packages/config", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.4", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}