# Configuration Docker pour FinShark

# Base de données
POSTGRES_PASSWORD=your_secure_password_here
DATABASE_URL=*************************************************************/finshark

# Redis
REDIS_URL=redis://redis:6379

# JWT et sécurité
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Services externes
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# API Keys
STRIPE_SECRET_KEY=sk_test_your_stripe_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Monitoring
SENTRY_DSN=https://<EMAIL>/project_id

# Environnement
NODE_ENV=development
RUST_LOG=debug
GIN_MODE=debug
