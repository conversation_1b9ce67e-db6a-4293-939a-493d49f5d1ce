-- Migration: Création de la table users pour FinShark
-- Date: 2024-07-29
-- Description: Table d'inscription des utilisateurs avec id, username, password_hash, created_at, updated_at

-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Créer la table users
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- C<PERSON>er un index unique sur username pour optimiser les recherches
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- <PERSON><PERSON>er un index sur created_at pour optimiser les tris
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger pour mettre à jour automatiquement updated_at
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE users IS 'Table des utilisateurs FinShark pour l''inscription et l''authentification';
COMMENT ON COLUMN users.id IS 'Identifiant unique UUID de l''utilisateur';
COMMENT ON COLUMN users.username IS 'Nom d''utilisateur unique (3-50 caractères)';
COMMENT ON COLUMN users.password_hash IS 'Hash bcrypt du mot de passe utilisateur';
COMMENT ON COLUMN users.created_at IS 'Date et heure de création du compte';
COMMENT ON COLUMN users.updated_at IS 'Date et heure de dernière modification';

-- Exemple d'insertion (à supprimer en production)
-- INSERT INTO users (username, password_hash) 
-- VALUES ('admin', crypt('admin123!', gen_salt('bf', 12)));
