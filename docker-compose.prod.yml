version: '3.8'

services:
  # Frontend Next.js (Production)
  frontend:
    build:
      context: ./frontend/web
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    networks:
      - finshark-network
    restart: unless-stopped
    depends_on:
      - backend-users
      - backend-auth
      - backend-vault
      - backend-notif

  # Backend NestJS (Users) - Production
  backend-users:
    build:
      context: ./backend/users
      dockerfile: Dockerfile
      target: production
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=postgresql://finshark:${POSTGRES_PASSWORD}@postgres:5432/finshark_users
    networks:
      - finshark-network
    restart: unless-stopped
    depends_on:
      - postgres

  # Backend Rust (Vault) - Production
  backend-vault:
    build:
      context: ./backend/vault
      dockerfile: Dockerfile
      target: production
    ports:
      - "8080:8080"
    environment:
      - RUST_LOG=info
    networks:
      - finshark-network
    restart: unless-stopped

  # Backend Go (Auth) - Production
  backend-auth:
    build:
      context: ./backend/auth
      dockerfile: Dockerfile
      target: production
    ports:
      - "8003:8003"
    environment:
      - GIN_MODE=release
      - DATABASE_URL=postgresql://finshark:${POSTGRES_PASSWORD}@postgres:5432/finshark_auth
    networks:
      - finshark-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis

  # Backend Python (Notifications) - Production
  backend-notif:
    build:
      context: ./backend/notif
      dockerfile: Dockerfile
      target: production
    ports:
      - "8002:8002"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://finshark:${POSTGRES_PASSWORD}@postgres:5432/finshark_notif
      - REDIS_URL=redis://redis:6379
    networks:
      - finshark-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis

  # Base de données PostgreSQL - Production
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=finshark
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=finshark
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - finshark-network
    restart: unless-stopped

  # Redis - Production
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - finshark-network
    restart: unless-stopped

  # Nginx reverse proxy - Production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - finshark-network
    restart: unless-stopped
    depends_on:
      - frontend
      - backend-users
      - backend-auth
      - backend-vault
      - backend-notif

volumes:
  postgres_data:
  redis_data:

networks:
  finshark-network:
    driver: bridge
