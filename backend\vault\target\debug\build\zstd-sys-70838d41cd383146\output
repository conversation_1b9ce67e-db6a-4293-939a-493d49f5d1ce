cargo:rerun-if-env-changed=ZSTD_SYS_USE_PKG_CONFIG
OUT_DIR = Some(/app/target/debug/build/zstd-sys-70838d41cd383146/out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-unknown-linux-musl)
HOST = Some(x86_64-unknown-linux-musl)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-musl
CC_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_musl
CC_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
CARGO_ENCODED_RUSTFLAGS = Some()
OUT_DIR = Some(/app/target/debug/build/zstd-sys-70838d41cd383146/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-unknown-linux-musl)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2)
HOST = Some(x86_64-unknown-linux-musl)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
OUT_DIR = Some(/app/target/debug/build/zstd-sys-70838d41cd383146/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-unknown-linux-musl)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2)
HOST = Some(x86_64-unknown-linux-musl)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
OUT_DIR = Some(/app/target/debug/build/zstd-sys-70838d41cd383146/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-unknown-linux-musl)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2)
HOST = Some(x86_64-unknown-linux-musl)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_musl
CFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-musl
CFLAGS_x86_64-unknown-linux-musl = None
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
exit status: 0
cargo:rerun-if-env-changed=AR_x86_64-unknown-linux-musl
AR_x86_64-unknown-linux-musl = None
cargo:rerun-if-env-changed=AR_x86_64_unknown_linux_musl
AR_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_unknown_linux_musl
ARFLAGS_x86_64_unknown_linux_musl = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-unknown-linux-musl
ARFLAGS_x86_64-unknown-linux-musl = None
cargo:rustc-link-lib=static=zstd
cargo:rustc-link-search=native=/app/target/debug/build/zstd-sys-70838d41cd383146/out
cargo:root=/app/target/debug/build/zstd-sys-70838d41cd383146/out
cargo:include=/usr/local/cargo/registry/src/index.crates.io-6f17d22bba15001f/zstd-sys-2.0.15+zstd.1.5.7/zstd/lib
