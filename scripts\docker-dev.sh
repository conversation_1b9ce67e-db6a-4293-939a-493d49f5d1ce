#!/bin/bash

# Script pour lancer l'environnement de développement Docker

echo "🐳 Lancement de l'environnement de développement FinShark..."

# Vérifier si Docker est installé
if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier si Docker Compose est installé
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Créer le fichier .env s'il n'existe pas
if [ ! -f .env ]; then
    echo "📝 Création du fichier .env..."
    cp .env.example .env
    echo "⚠️  Veuillez modifier le fichier .env avec vos configurations."
fi

# Construire et lancer les services
echo "🔨 Construction des images Docker..."
docker-compose build

echo "🚀 Lancement des services..."
docker-compose up -d

# Attendre que les services soient prêts
echo "⏳ Attente du démarrage des services..."
sleep 10

# Vérifier l'état des services
echo "📊 État des services:"
docker-compose ps

echo ""
echo "✅ Environnement de développement prêt!"
echo ""
echo "🌐 Services disponibles:"
echo "   Frontend:      http://localhost:3000"
echo "   Users API:     http://localhost:3001"
echo "   Auth API:      http://localhost:8003"
echo "   Vault API:     http://localhost:8080"
echo "   Notif API:     http://localhost:8002"
echo "   PostgreSQL:    localhost:5432"
echo "   Redis:         localhost:6379"
echo ""
echo "📝 Commandes utiles:"
echo "   Voir les logs:     docker-compose logs -f"
echo "   Arrêter:          docker-compose down"
echo "   Redémarrer:       docker-compose restart"
echo "   Rebuild:          docker-compose up --build"
