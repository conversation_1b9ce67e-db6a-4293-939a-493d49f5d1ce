// Déclarations de types pour Next.js et next/font/google
declare module 'next' {
  export interface Metadata {
    title?: string | {
      default?: string;
      template?: string;
      absolute?: string;
    };
    description?: string;
    keywords?: string | string[];
    authors?: Array<{ name: string; url?: string }>;
    creator?: string;
    publisher?: string;
    formatDetection?: {
      email?: boolean;
      address?: boolean;
      telephone?: boolean;
    };
    generator?: string;
    applicationName?: string;
    referrer?: 'origin' | 'no-referrer' | 'no-referrer-when-downgrade' | 'origin-when-cross-origin' | 'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
    colorScheme?: 'light' | 'dark' | 'light dark';
    viewport?: string | {
      width?: string | number;
      height?: string | number;
      initialScale?: number;
      minimumScale?: number;
      maximumScale?: number;
      userScalable?: boolean;
      viewportFit?: 'auto' | 'contain' | 'cover';
    };
    themeColor?: string | Array<{ media?: string; color: string }>;
    manifest?: string;
    robots?: string | {
      index?: boolean;
      follow?: boolean;
      noindex?: boolean;
      nofollow?: boolean;
      noarchive?: boolean;
      nosnippet?: boolean;
      noimageindex?: boolean;
      nocache?: boolean;
    };
    icons?: string | Array<{
      url: string;
      type?: string;
      sizes?: string;
      color?: string;
      rel?: string;
    }>;
    openGraph?: {
      title?: string;
      description?: string;
      url?: string;
      siteName?: string;
      images?: Array<{
        url: string;
        width?: number;
        height?: number;
        alt?: string;
      }>;
      locale?: string;
      type?: string;
    };
    twitter?: {
      card?: 'summary' | 'summary_large_image' | 'app' | 'player';
      title?: string;
      description?: string;
      creator?: string;
      images?: string | Array<string>;
    };
    verification?: {
      google?: string;
      yandex?: string;
      yahoo?: string;
      other?: Record<string, string | string[]>;
    };
    appleWebApp?: {
      title?: string;
      statusBarStyle?: 'default' | 'black' | 'black-translucent';
      startupImage?: Array<{
        url: string;
        media?: string;
      }>;
    };
    alternates?: {
      canonical?: string;
      languages?: Record<string, string>;
      media?: Record<string, string>;
      types?: Record<string, string>;
    };
    appLinks?: {
      ios?: {
        url?: string;
        app_store_id?: string;
        app_name?: string;
      };
      android?: {
        package?: string;
        url?: string;
        class?: string;
        app_name?: string;
      };
      web?: {
        url?: string;
        should_fallback?: boolean;
      };
    };
    archives?: string[];
    assets?: string[];
    bookmarks?: string[];
    category?: string;
    classification?: string;
    other?: Record<string, string | number | Array<string | number>>;
  }
}

declare module 'next/font/google' {
  export interface FontConfig {
    subsets: string[];
    variable?: string;
    display?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
    weight?: string | string[];
    style?: string | string[];
    preload?: boolean;
    fallback?: string[];
    adjustFontFallback?: boolean;
  }

  export interface NextFont {
    className: string;
    style: {
      fontFamily: string;
      fontWeight?: string;
      fontStyle?: string;
    };
    variable?: string;
  }

  export function Playfair_Display(config: FontConfig): NextFont;
  export function Inter(config: FontConfig): NextFont;
  export function JetBrains_Mono(config: FontConfig): NextFont;
  export function Roboto(config: FontConfig): NextFont;
  export function Open_Sans(config: FontConfig): NextFont;
  export function Lato(config: FontConfig): NextFont;
  export function Montserrat(config: FontConfig): NextFont;
  export function Source_Sans_Pro(config: FontConfig): NextFont;
  export function Oswald(config: FontConfig): NextFont;
  export function Raleway(config: FontConfig): NextFont;
  export function PT_Sans(config: FontConfig): NextFont;
  export function Lora(config: FontConfig): NextFont;
  export function Nunito(config: FontConfig): NextFont;
  export function Poppins(config: FontConfig): NextFont;
  export function Merriweather(config: FontConfig): NextFont;
  export function Ubuntu(config: FontConfig): NextFont;
  export function Fira_Sans(config: FontConfig): NextFont;
  export function Crimson_Text(config: FontConfig): NextFont;
  export function Droid_Sans(config: FontConfig): NextFont;
  export function Noto_Sans(config: FontConfig): NextFont;
}

declare module 'next/font/local' {
  export interface LocalFontConfig {
    src: string | Array<{
      path: string;
      weight?: string;
      style?: string;
    }>;
    variable?: string;
    display?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
    weight?: string;
    style?: string;
    preload?: boolean;
    fallback?: string[];
    adjustFontFallback?: boolean;
  }

  export interface NextFont {
    className: string;
    style: {
      fontFamily: string;
      fontWeight?: string;
      fontStyle?: string;
    };
    variable?: string;
  }

  export default function localFont(config: LocalFontConfig): NextFont;
}
