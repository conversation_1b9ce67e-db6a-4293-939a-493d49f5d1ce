// Déclarations de types pour next/font/google
declare module 'next/font/google' {
  export interface FontConfig {
    subsets: string[];
    variable?: string;
    display?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
    weight?: string | string[];
    style?: string | string[];
    preload?: boolean;
    fallback?: string[];
    adjustFontFallback?: boolean;
  }

  export interface NextFont {
    className: string;
    style: {
      fontFamily: string;
      fontWeight?: string;
      fontStyle?: string;
    };
    variable?: string;
  }

  export function Playfair_Display(config: FontConfig): NextFont;
  export function Inter(config: FontConfig): NextFont;
  export function JetBrains_Mono(config: FontConfig): NextFont;
  export function Roboto(config: FontConfig): NextFont;
  export function Open_Sans(config: FontConfig): NextFont;
  export function Lato(config: FontConfig): NextFont;
  export function Montserrat(config: FontConfig): NextFont;
  export function Source_Sans_Pro(config: FontConfig): NextFont;
  export function Oswald(config: FontConfig): NextFont;
  export function Raleway(config: FontConfig): NextFont;
  export function PT_Sans(config: FontConfig): NextFont;
  export function Lora(config: FontConfig): NextFont;
  export function Nunito(config: FontConfig): NextFont;
  export function Poppins(config: FontConfig): NextFont;
  export function Merriweather(config: FontConfig): NextFont;
  export function Ubuntu(config: FontConfig): NextFont;
  export function Fira_Sans(config: FontConfig): NextFont;
  export function Crimson_Text(config: FontConfig): NextFont;
  export function Droid_Sans(config: FontConfig): NextFont;
  export function Noto_Sans(config: FontConfig): NextFont;
}

declare module 'next/font/local' {
  export interface LocalFontConfig {
    src: string | Array<{
      path: string;
      weight?: string;
      style?: string;
    }>;
    variable?: string;
    display?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
    weight?: string;
    style?: string;
    preload?: boolean;
    fallback?: string[];
    adjustFontFallback?: boolean;
  }

  export interface NextFont {
    className: string;
    style: {
      fontFamily: string;
      fontWeight?: string;
      fontStyle?: string;
    };
    variable?: string;
  }

  export default function localFont(config: LocalFontConfig): NextFont;
}
