{"rustc": 12848521558442915983, "features": "[\"as_ref\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 14730367966619198579, "profile": 17200226775133217513, "path": 10535503392951374985, "deps": [[10884279900893228269, "derive_more_impl", false, 9210700110351466637]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-72c6731c41d9bb84/dep-lib-derive_more"}}], "rustflags": [], "metadata": 8950704488499756937, "config": 2202906307356721367, "compile_kind": 0}