# 🦈 FinShark Web

Interface web moderne pour l'application de gestion financière FinShark.

## 🚀 Technologies

- **Next.js 15.4.4** - Framework React avec App Router
- **TypeScript** - Typage statique
- **Tailwind CSS v4** - Framework CSS utilitaire
- **Google Fonts** - Polices optimisées (Playfair Display, Inter, JetBrains Mono)

## 🎨 Design System

### Couleurs
- **Shark** - Palette principale (bleus profonds)
- **Platinum** - Palette neutre (gris élégants)
- **Emerald** - Couleur d'accent (vert moderne)

### Typographie
- **Display** - Playfair Display (titres élégants)
- **Body** - Inter (texte lisible)
- **Mono** - JetBrains Mono (code)

## 🛠️ Développement

### Prérequis
- Node.js 20+
- Docker & Docker Compose

### Installation
```bash
# Depuis la racine du projet
docker-compose up frontend
```

### Scripts disponibles
```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run start        # Serveur de production
npm run lint         # Linting ESLint
npm run type-check   # Vérification TypeScript
```

## 📁 Structure

```
src/
├── app/                 # App Router Next.js
│   ├── layout.tsx      # Layout principal
│   ├── page.tsx        # Page d'accueil
│   └── globals.css     # Styles globaux
└── types/              # Déclarations TypeScript
    ├── next.d.ts       # Types Next.js
    └── next-font.d.ts  # Types polices
```

## 🌐 URLs

- **Développement** : http://localhost:3000
- **Production** : TBD

## 📝 Notes

- Configuration Tailwind CSS v4 avec design system FinShark
- Types TypeScript personnalisés pour Next.js 15
- Hot reload activé pour le développement
- Build optimisé pour la production avec mode standalone
