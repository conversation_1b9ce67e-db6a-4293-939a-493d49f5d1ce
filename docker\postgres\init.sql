-- Script d'initialisation PostgreSQL pour FinShark

-- <PERSON><PERSON>er les bases de données pour chaque service
CREATE DATABASE finshark_users;
CREATE DATABASE finshark_auth;
CREATE DATABASE finshark_notif;
CREATE DATABASE finshark_vault;

-- C<PERSON>er un utilisateur pour chaque service (optionnel)
CREATE USER finshark_users_user WITH PASSWORD 'users_password';
CREATE USER finshark_auth_user WITH PASSWORD 'auth_password';
CREATE USER finshark_notif_user WITH PASSWORD 'notif_password';
CREATE USER finshark_vault_user WITH PASSWORD 'vault_password';

-- Accorder les privilèges
GRANT ALL PRIVILEGES ON DATABASE finshark_users TO finshark_users_user;
GRANT ALL PRIVILEGES ON DATABASE finshark_auth TO finshark_auth_user;
GRANT ALL PRIVILEGES ON DATABASE finshark_notif TO finshark_notif_user;
GRANT ALL PRIVILEGES ON DATABASE finshark_vault TO finshark_vault_user;

-- Accorder les privilèges à l'utilisateur principal
GRANT ALL PRIVILEGES ON DATABASE finshark_users TO finshark;
GRANT ALL PRIVILEGES ON DATABASE finshark_auth TO finshark;
GRANT ALL PRIVILEGES ON DATABASE finshark_notif TO finshark;
GRANT ALL PRIVILEGES ON DATABASE finshark_vault TO finshark;

-- Extensions utiles
\c finshark_users;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c finshark_auth;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c finshark_notif;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c finshark_vault;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
