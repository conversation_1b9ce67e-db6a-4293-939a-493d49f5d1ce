import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

// Configuration ESLint partagée pour tous les projets FinShark
export const baseConfig = [
  ...compat.extends('eslint:recommended', '@typescript-eslint/recommended'),
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      // Règles générales
      'no-console': 'warn',
      'no-debugger': 'error',
      'no-unused-vars': 'off', // Désactivé car TypeScript le gère
      '@typescript-eslint/no-unused-vars': [
        'error',
        { argsIgnorePattern: '^_' },
      ],
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-non-null-assertion': 'warn',

      // Règles de style
      'prefer-const': 'error',
      'no-var': 'error',
      'object-shorthand': 'error',
      'prefer-template': 'error',
    },
  },
];

// Configuration spécifique pour React/Next.js
export const reactConfig = [
  ...baseConfig,
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  {
    rules: {
      // Règles React spécifiques
      'react/react-in-jsx-scope': 'off', // Next.js n'en a pas besoin
      'react/prop-types': 'off', // TypeScript gère les types
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      'jsx-a11y/alt-text': 'error',
      'jsx-a11y/aria-props': 'error',
      'jsx-a11y/aria-proptypes': 'error',
    },
  },
];

// Configuration spécifique pour React Native/Expo
export const reactNativeConfig = [
  ...baseConfig,
  ...compat.extends('@react-native-community', 'expo'),
  {
    rules: {
      // Règles React Native spécifiques
      'react-native/no-unused-styles': 'error',
      'react-native/split-platform-components': 'error',
      'react-native/no-inline-styles': 'warn',
      'react-native/no-color-literals': 'warn',
    },
  },
];

export default baseConfig;
