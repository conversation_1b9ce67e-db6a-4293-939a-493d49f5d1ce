// Configuration PostCSS pour l'application web FinShark
// Configuration autonome pour Tailwind CSS v4

/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: [
    // Plugin Tailwind CSS v4
    '@tailwindcss/postcss',
    // Autoprefixer pour la compatibilité navigateurs
    'autoprefixer',
    // Plugin pour les variables CSS personnalisées (optionnel)
    // 'postcss-custom-properties',
  ],
};

export default config;
