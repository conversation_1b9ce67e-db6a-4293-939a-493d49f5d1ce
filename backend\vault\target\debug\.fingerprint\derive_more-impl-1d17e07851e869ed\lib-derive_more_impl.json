{"rustc": 12848521558442915983, "features": "[\"as_ref\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 6699540842379466726, "profile": 10152260758775034917, "path": 1562289305574629216, "deps": [[3476769717318978119, "unicode_xid", false, 7939066842334136172], [14125489736606713511, "syn", false, 2314701416501865681], [17525013869477438691, "quote", false, 15303343552696472372], [18036439996138669183, "proc_macro2", false, 1696047517055707730]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-impl-1d17e07851e869ed/dep-lib-derive_more_impl"}}], "rustflags": [], "metadata": 18390218411190410780, "config": 2202906307356721367, "compile_kind": 0}