{"rustc_fingerprint": 2304348860445005647, "outputs": {"4614504638168534921": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.82.0 (f6e511eec 2024-10-15)\nbinary: rustc\ncommit-hash: f6e511eec7342f59a25f7c0534f1dbea00d01b14\ncommit-date: 2024-10-15\nhost: x86_64-unknown-linux-musl\nrelease: 1.82.0\nLLVM version: 19.1.1\n", "stderr": ""}, "15729799797837862367": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\nlib___.so\n/usr/local/rustup/toolchains/1.82.0-x86_64-unknown-linux-musl\noff\npacked\nunpacked\n___\ndebug_assertions\npanic=\"unwind\"\nproc_macro\ntarget_abi=\"\"\ntarget_arch=\"x86_64\"\ntarget_endian=\"little\"\ntarget_env=\"musl\"\ntarget_family=\"unix\"\ntarget_feature=\"fxsr\"\ntarget_feature=\"sse\"\ntarget_feature=\"sse2\"\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_os=\"linux\"\ntarget_pointer_width=\"64\"\ntarget_vendor=\"unknown\"\nunix\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `x86_64-unknown-linux-musl`\n\nwarning: dropping unsupported crate type `cdylib` for target `x86_64-unknown-linux-musl`\n\nwarning: 2 warnings emitted\n\n"}}, "successes": {}}