# Dockerfile pour le service Rust (Vault)
FROM rust:1.75-alpine AS base

# Installer les dépendances système
RUN apk add --no-cache \
    musl-dev \
    pkgconfig \
    openssl-dev

WORKDIR /app

# Copier les fichiers de configuration Cargo
COPY Cargo.toml Cargo.lock ./

# Stage de développement
FROM base AS development
COPY . .
EXPOSE 8080
CMD ["cargo", "run"]

# Stage de build
FROM base AS build

# Créer un projet dummy pour cache les dépendances
RUN mkdir src && echo "fn main() {}" > src/main.rs
RUN cargo build --release
RUN rm -rf src

# Copier le code source réel
COPY src ./src

# Build de l'application
RUN touch src/main.rs
RUN cargo build --release

# Stage de production
FROM alpine:3.18 AS production

# Installer les dépendances runtime
RUN apk add --no-cache \
    ca-certificates \
    libgcc

# Créer un utilisateur non-root
RUN addgroup -g 1001 -S rust
RUN adduser -S rust -u 1001

WORKDIR /app

# Copier le binaire
COPY --from=build --chown=rust:rust /app/target/release/vault ./vault

USER rust

EXPOSE 8080

# Variables d'environnement
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

CMD ["./vault"]
