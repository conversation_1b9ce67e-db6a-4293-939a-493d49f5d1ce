{"version": 3, "file": "windowTime.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/windowTime.ts"], "names": [], "mappings": ";;;AAAA,sCAAqC;AACrC,4CAAoD;AAEpD,gDAA+C;AAE/C,qCAAuC;AACvC,2DAAgE;AAChE,+CAA8C;AAC9C,qCAA4C;AAC5C,2DAA0D;AAgG1D,SAAgB,UAAU,CAAI,cAAsB;;IAAE,mBAAmB;SAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;QAAnB,kCAAmB;;IACvE,IAAM,SAAS,GAAG,MAAA,mBAAY,CAAC,SAAS,CAAC,mCAAI,sBAAc,CAAC;IAC5D,IAAM,sBAAsB,GAAG,MAAC,SAAS,CAAC,CAAC,CAAY,mCAAI,IAAI,CAAC;IAChE,IAAM,aAAa,GAAI,SAAS,CAAC,CAAC,CAAY,IAAI,QAAQ,CAAC;IAE3D,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAEhC,IAAI,aAAa,GAA6B,EAAE,CAAC;QAGjD,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,IAAM,WAAW,GAAG,UAAC,MAAkD;YAC7D,IAAA,MAAM,GAAW,MAAM,OAAjB,EAAE,IAAI,GAAK,MAAM,KAAX,CAAY;YAChC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,qBAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YACjC,cAAc,IAAI,WAAW,EAAE,CAAC;QAClC,CAAC,CAAC;QAMF,IAAM,WAAW,GAAG;YAClB,IAAI,aAAa,EAAE;gBACjB,IAAM,IAAI,GAAG,IAAI,2BAAY,EAAE,CAAC;gBAChC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACrB,IAAM,QAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;gBAChC,IAAM,QAAM,GAAG;oBACb,MAAM,UAAA;oBACN,IAAI,MAAA;oBACJ,IAAI,EAAE,CAAC;iBACR,CAAC;gBACF,aAAa,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;gBAC3B,UAAU,CAAC,IAAI,CAAC,QAAM,CAAC,YAAY,EAAE,CAAC,CAAC;gBACvC,iCAAe,CAAC,IAAI,EAAE,SAAS,EAAE,cAAM,OAAA,WAAW,CAAC,QAAM,CAAC,EAAnB,CAAmB,EAAE,cAAc,CAAC,CAAC;aAC7E;QACH,CAAC,CAAC;QAEF,IAAI,sBAAsB,KAAK,IAAI,IAAI,sBAAsB,IAAI,CAAC,EAAE;YAIlE,iCAAe,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;SACnF;aAAM;YACL,cAAc,GAAG,IAAI,CAAC;SACvB;QAED,WAAW,EAAE,CAAC;QAQd,IAAM,IAAI,GAAG,UAAC,EAAqC,IAAK,OAAA,aAAc,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,EAAlC,CAAkC,CAAC;QAM3F,IAAM,SAAS,GAAG,UAAC,EAAqC;YACtD,IAAI,CAAC,UAAC,EAAU;oBAAR,MAAM,YAAA;gBAAO,OAAA,EAAE,CAAC,MAAM,CAAC;YAAV,CAAU,CAAC,CAAC;YACjC,EAAE,CAAC,UAAU,CAAC,CAAC;YACf,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC,CAAC;QAEF,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAQ;YAEP,IAAI,CAAC,UAAC,MAAM;gBACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE1B,aAAa,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,EAED,cAAM,OAAA,SAAS,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,QAAQ,EAAE,EAAnB,CAAmB,CAAC,EAA5C,CAA4C,EAElD,UAAC,GAAG,IAAK,OAAA,SAAS,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAnB,CAAmB,CAAC,EAA5C,CAA4C,CACtD,CACF,CAAC;QAKF,OAAO;YAEL,aAAa,GAAG,IAAK,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AA/FD,gCA+FC"}