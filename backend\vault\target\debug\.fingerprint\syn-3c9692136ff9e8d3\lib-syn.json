{"rustc": 12848521558442915983, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 14790166133050072309, "profile": 8861558584828204938, "path": 14177687073893426637, "deps": [[7006636483571730090, "unicode_ident", false, 9238681612317049495], [17525013869477438691, "quote", false, 15303343552696472372], [18036439996138669183, "proc_macro2", false, 1696047517055707730]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-3c9692136ff9e8d3/dep-lib-syn"}}], "rustflags": [], "metadata": 6886477143387768027, "config": 2202906307356721367, "compile_kind": 0}