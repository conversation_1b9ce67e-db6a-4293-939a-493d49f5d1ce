#!/bin/bash

# Script d'initialisation complète du projet FinShark

echo "🚀 Initialisation complète du projet FinShark..."
echo "================================================"

# Fonction pour afficher les étapes
step() {
    echo ""
    echo "📋 $1"
    echo "----------------------------------------"
}

# Vérifier les prérequis
step "Vérification des prérequis"

if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi
echo "✅ Node.js installé ($(node --version))"

if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi
echo "✅ Docker installé"

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi
echo "✅ Docker Compose installé"

# Initialiser les variables d'environnement
step "Configuration des variables d'environnement"

if [ ! -f .env ]; then
    echo "📝 Création du fichier .env..."
    cp .env.example .env
    echo "✅ Fichier .env créé. Veuillez le modifier avec vos configurations."
else
    echo "✅ Fichier .env existe déjà"
fi

# Installer les dépendances racine
step "Installation des dépendances racine"
npm install

# Installer les dépendances frontend
step "Installation des dépendances frontend"

echo "📦 Configuration centralisée..."
cd frontend/packages/config
npm install
cd ../../..

echo "📦 Frontend web (Next.js)..."
cd frontend/web
npm install
cd ../..

echo "📦 Frontend mobile (Expo)..."
cd frontend/mobile
npm install
cd ../..

# Installer les dépendances backend
step "Installation des dépendances backend"

echo "📦 Backend global..."
cd backend
npm install
cd ..

echo "📦 Service Users (NestJS)..."
cd backend/users
npm install
cd ../..

echo "📦 Service Auth (Go)..."
cd backend/auth
if command -v go &> /dev/null; then
    go mod tidy
    echo "✅ Dépendances Go installées"
else
    echo "⚠️ Go non installé, dépendances non installées"
fi
cd ../..

echo "📦 Service Vault (Rust)..."
cd backend/vault
if command -v cargo &> /dev/null; then
    cargo build
    echo "✅ Dépendances Rust installées"
else
    echo "⚠️ Rust non installé, dépendances non installées"
fi
cd ../..

echo "📦 Service Notifications (Python)..."
cd backend/notif
if command -v python3 &> /dev/null; then
    python3 -m venv venv
    source venv/bin/activate 2>/dev/null || source venv/Scripts/activate
    pip install -r requirements.txt
    echo "✅ Dépendances Python installées"
else
    echo "⚠️ Python non installé, dépendances non installées"
fi
cd ../..

# Construire les images Docker
step "Construction des images Docker"

echo "🐳 Construction des images Docker..."
docker-compose build

# Vérification finale
step "Vérification finale"

echo "🔍 Exécution du script de vérification..."
chmod +x scripts/check-project.sh
./scripts/check-project.sh

echo ""
echo "🎉 Initialisation terminée !"
echo "============================"
echo ""
echo "🚀 Commandes pour démarrer:"
echo "  npm run dev              # Développement local"
echo "  npm run docker:dev       # Docker développement"
echo "  npm run docker:prod      # Docker production"
echo ""
echo "📊 Services disponibles après démarrage:"
echo "  Frontend:      http://localhost:3000"
echo "  Users API:     http://localhost:3001"
echo "  Auth API:      http://localhost:8003"
echo "  Vault API:     http://localhost:8080"
echo "  Notif API:     http://localhost:8002"
echo ""
echo "📝 Consultez docker/README.md pour plus d'informations"
