import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpStatus,
  HttpCode,
  ValidationPipe,
  ParseUUIDPipe,
  Query,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UserResponseDto } from './dto/user-response.dto';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Inscription d'un nouvel utilisateur
   * POST /users/register
   */
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async register(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createUserDto: CreateUserDto,
  ): Promise<{
    message: string;
    user: UserResponseDto;
  }> {
    const user = await this.usersService.create(createUserDto);
    
    return {
      message: 'Utilisateur créé avec succès',
      user,
    };
  }

  /**
   * Vérifier la disponibilité d'un nom d'utilisateur
   * GET /users/check-username?username=test
   */
  @Get('check-username')
  async checkUsername(
    @Query('username') username: string,
  ): Promise<{
    available: boolean;
    message: string;
  }> {
    if (!username) {
      return {
        available: false,
        message: 'Nom d\'utilisateur requis',
      };
    }

    const available = await this.usersService.isUsernameAvailable(username);
    
    return {
      available,
      message: available 
        ? 'Nom d\'utilisateur disponible' 
        : 'Nom d\'utilisateur déjà utilisé',
    };
  }

  /**
   * Obtenir tous les utilisateurs (pour l'administration)
   * GET /users
   */
  @Get()
  async findAll(): Promise<{
    message: string;
    users: UserResponseDto[];
    count: number;
  }> {
    const users = await this.usersService.findAll();
    
    return {
      message: 'Utilisateurs récupérés avec succès',
      users,
      count: users.length,
    };
  }

  /**
   * Obtenir un utilisateur par son ID
   * GET /users/:id
   */
  @Get(':id')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<{
    message: string;
    user: UserResponseDto;
  }> {
    const user = await this.usersService.findOne(id);
    
    return {
      message: 'Utilisateur trouvé',
      user,
    };
  }

  /**
   * Obtenir un utilisateur par son nom d'utilisateur
   * GET /users/username/:username
   */
  @Get('username/:username')
  async findByUsername(
    @Param('username') username: string,
  ): Promise<{
    message: string;
    user: UserResponseDto;
  }> {
    const user = await this.usersService.findByUsername(username);
    
    return {
      message: 'Utilisateur trouvé',
      user,
    };
  }

  /**
   * Supprimer un utilisateur
   * DELETE /users/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.usersService.remove(id);
  }
}
