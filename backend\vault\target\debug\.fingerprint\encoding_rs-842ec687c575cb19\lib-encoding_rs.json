{"rustc": 12848521558442915983, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 5079274106931611199, "profile": 16690480377348987070, "path": 10352085933916043946, "deps": [[5682297152023424035, "cfg_if", false, 3049496781248565077]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-842ec687c575cb19/dep-lib-encoding_rs"}}], "rustflags": [], "metadata": 10075669053249481654, "config": 2202906307356721367, "compile_kind": 0}