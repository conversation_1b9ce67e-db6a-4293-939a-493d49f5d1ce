{"name": "@finshark/config", "version": "1.0.0", "type": "module", "main": "index.js", "description": "Configuration partagée pour tous les projets frontend FinShark", "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "keywords": ["config", "eslint", "prettier", "tailwind", "typescript"], "author": "FinShark Team", "license": "MIT", "exports": {"./eslint": "./eslint.config.js", "./postcss": "./postcss.config.js", "./tailwind": "./tailwind.config.js", "./tsconfig": "./tsconfig.base.json", "./prettier": "./prettier.config.js"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.17.10", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-jsx-a11y": "^6.10.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}