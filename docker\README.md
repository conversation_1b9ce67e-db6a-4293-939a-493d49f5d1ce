# 🐳 Configuration Docker FinShark

Cette documentation explique comment utiliser Docker avec le projet FinShark.

## 📋 Prérequis

- Docker (version 20.10+)
- Docker Compose (version 2.0+)
- Git

## 🚀 Démarrage rapide

### Développement

```bash
# Cloner le projet
git clone <repository-url>
cd finShark-co

# Copier et configurer les variables d'environnement
cp .env.example .env
# Modifier .env avec vos configurations

# Lancer l'environnement de développement
npm run docker:dev

# Ou avec le script
chmod +x scripts/docker-dev.sh
./scripts/docker-dev.sh
```

### Production

```bash
# Configurer les variables d'environnement de production
export POSTGRES_PASSWORD="your_secure_password"

# Lancer en production
npm run docker:prod

# Ou avec le script
chmod +x scripts/docker-prod.sh
./scripts/docker-prod.sh
```

## 🏗️ Architecture

### Services

| Service | Port | Description |
|---------|------|-------------|
| frontend | 3000 | Next.js (React) |
| backend-users | 3001 | NestJS (Users API) |
| backend-auth | 8003 | Go (Authentication) |
| backend-vault | 8080 | Rust (Vault/Security) |
| backend-notif | 8002 | Python (Notifications) |
| postgres | 5432 | Base de données PostgreSQL |
| redis | 6379 | Cache et sessions |
| nginx | 80/443 | Reverse proxy |

### Réseau

Tous les services communiquent via le réseau Docker `finshark-network`.

## 📝 Scripts disponibles

```bash
# Développement
npm run docker:dev              # Lancer en mode développement
npm run docker:dev:detached     # Lancer en arrière-plan

# Production
npm run docker:prod             # Lancer en production

# Gestion
npm run docker:stop             # Arrêter les services
npm run docker:logs             # Voir les logs
npm run docker:clean            # Nettoyer Docker

# Docker Compose direct
docker-compose up --build       # Développement
docker-compose -f docker-compose.prod.yml up -d --build  # Production
```

## 🔧 Configuration

### Variables d'environnement

Copiez `.env.example` vers `.env` et configurez :

```env
# Base de données
POSTGRES_PASSWORD=your_secure_password

# JWT et sécurité
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key

# Services externes
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### Volumes

- `postgres_data` : Données PostgreSQL persistantes
- `redis_data` : Données Redis persistantes
- Code source monté en développement pour le hot reload

## 🔍 Monitoring et debugging

### Logs

```bash
# Tous les services
docker-compose logs -f

# Service spécifique
docker-compose logs -f frontend
docker-compose logs -f backend-users

# Dernières lignes
docker-compose logs --tail=100 backend-auth
```

### État des services

```bash
# Voir l'état
docker-compose ps

# Statistiques d'utilisation
docker stats

# Inspecter un conteneur
docker inspect finshark-frontend
```

### Accès aux conteneurs

```bash
# Shell dans un conteneur
docker-compose exec frontend sh
docker-compose exec postgres psql -U finshark -d finshark

# Exécuter une commande
docker-compose exec backend-users npm run test
```

## 🛠️ Développement

### Hot reload

En mode développement, le code source est monté comme volume, permettant le hot reload :

- **Frontend** : Next.js hot reload automatique
- **Backend NestJS** : Nodemon pour le restart automatique
- **Backend Go** : Air pour le hot reload (à configurer)
- **Backend Rust** : Cargo watch (à configurer)
- **Backend Python** : Uvicorn reload automatique

### Base de données

```bash
# Accéder à PostgreSQL
docker-compose exec postgres psql -U finshark -d finshark

# Backup
docker-compose exec postgres pg_dump -U finshark finshark > backup.sql

# Restore
docker-compose exec -T postgres psql -U finshark finshark < backup.sql
```

## 🚀 Déploiement

### Production

1. Configurer les variables d'environnement
2. Utiliser `docker-compose.prod.yml`
3. Configurer SSL/TLS pour Nginx
4. Mettre en place la surveillance

### CI/CD

Exemple avec GitHub Actions :

```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          docker-compose -f docker-compose.prod.yml up -d --build
```

## 🔒 Sécurité

- Utilisateurs non-root dans tous les conteneurs
- Secrets via variables d'environnement
- Rate limiting avec Nginx
- Health checks pour tous les services
- Volumes séparés pour les données sensibles

## 📊 Performance

### Optimisations

- Multi-stage builds pour réduire la taille des images
- Cache des dépendances
- Compression gzip avec Nginx
- Images Alpine Linux légères

### Monitoring

- Health checks intégrés
- Logs centralisés
- Métriques avec Docker stats

## 🆘 Dépannage

### Problèmes courants

1. **Port déjà utilisé** : Changer les ports dans docker-compose.yml
2. **Permissions** : Vérifier les droits sur les volumes
3. **Mémoire insuffisante** : Augmenter la limite Docker
4. **Build échoue** : Nettoyer le cache avec `docker system prune`

### Commandes utiles

```bash
# Nettoyer complètement
docker system prune -a --volumes

# Reconstruire sans cache
docker-compose build --no-cache

# Redémarrer un service
docker-compose restart backend-users

# Voir les ressources utilisées
docker system df
```
