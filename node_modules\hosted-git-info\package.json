{"name": "hosted-git-info", "version": "7.0.2", "description": "Provides metadata and conversions from repository urls for GitHub, Bitbucket and GitLab", "main": "./lib/index.js", "repository": {"type": "git", "url": "git+https://github.com/npm/hosted-git-info.git"}, "keywords": ["git", "github", "bitbucket", "gitlab"], "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/hosted-git-info/issues"}, "homepage": "https://github.com/npm/hosted-git-info", "scripts": {"posttest": "npm run lint", "snap": "tap", "test": "tap", "test:coverage": "tap --coverage-report=html", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "dependencies": {"lru-cache": "^10.0.1"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "engines": {"node": "^16.14.0 || >=18.0.0"}, "tap": {"color": 1, "coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": "true"}}