{"name": "finshark-co", "version": "1.0.0", "main": "index.js", "private": true, "scripts": {"dev": "concurrently --names \"frontend,backend\" --prefix-colors \"cyan,yellow\" \"npm run dev --prefix frontend\" \"npm run dev:all --prefix backend\"", "dev:frontend": "npm run dev --prefix frontend/web", "dev:backend": "npm run dev:all --prefix backend", "dev:web": "npm run dev --prefix frontend/web", "dev:mobile": "npm run dev --prefix frontend/mobile", "docker:dev": "docker-compose up --build", "docker:dev:detached": "docker-compose up -d --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d --build", "docker:stop": "docker-compose down", "docker:stop:prod": "docker-compose -f docker-compose.prod.yml down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker system prune -f && docker volume prune -f"}, "keywords": [], "author": "", "license": "ISC", "description": "La racine du projet Finshark", "dependencies": {"concurrently": "^9.2.0"}, "workspaces": ["frontend/web", "frontend/mobile", "backend"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}