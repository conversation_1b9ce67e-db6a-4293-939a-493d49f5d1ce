import { useState, useEffect, createContext, useContext } from 'react';

// Types
export interface User {
  id: string;
  username: string;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface AuthContextType extends AuthState {
  login: (username: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (username: string, password: string) => Promise<{ success: boolean; user?: User; error?: string }>;
  logout: () => void;
  checkUsernameAvailability: (username: string) => Promise<{ available: boolean; error?: string }>;
}

// Configuration de l'API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

// Hook useAuth
export const useAuth = (): AuthContextType => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true
  });

  // Vérifier l'authentification au chargement
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Vérifier le statut d'authentification
  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('finshark_token');
      if (!token) {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      // Ici, vous pourriez vérifier la validité du token avec l'API
      // Pour l'instant, on simule une vérification
      const userData = localStorage.getItem('finshark_user');
      if (userData) {
        const user = JSON.parse(userData);
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false
        });
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'authentification:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Fonction de connexion
  const login = async (username: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (response.ok) {
        // Stocker le token et les données utilisateur
        localStorage.setItem('finshark_token', data.token);
        localStorage.setItem('finshark_user', JSON.stringify(data.user));

        setAuthState({
          user: data.user,
          isAuthenticated: true,
          isLoading: false
        });

        return { success: true };
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { success: false, error: data.message || 'Erreur de connexion' };
      }
    } catch (error) {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return { success: false, error: 'Erreur de connexion au serveur' };
    }
  };

  // Fonction d'inscription
  const register = async (username: string, password: string): Promise<{ success: boolean; user?: User; error?: string }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await fetch(`${API_BASE_URL}/users/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (response.ok) {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { success: true, user: data.user };
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { success: false, error: data.message || 'Erreur lors de l\'inscription' };
      }
    } catch (error) {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return { success: false, error: 'Erreur de connexion au serveur' };
    }
  };

  // Fonction de déconnexion
  const logout = () => {
    localStorage.removeItem('finshark_token');
    localStorage.removeItem('finshark_user');
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false
    });
  };

  // Vérifier la disponibilité d'un nom d'utilisateur
  const checkUsernameAvailability = async (username: string): Promise<{ available: boolean; error?: string }> => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/check-username?username=${encodeURIComponent(username)}`);
      const data = await response.json();

      if (response.ok) {
        return { available: data.available };
      } else {
        return { available: false, error: data.message || 'Erreur lors de la vérification' };
      }
    } catch (error) {
      return { available: false, error: 'Erreur de connexion au serveur' };
    }
  };

  return {
    ...authState,
    login,
    register,
    logout,
    checkUsernameAvailability
  };
};

// Context pour l'authentification (optionnel, pour une utilisation globale)
export const AuthContext = createContext<AuthContextType | null>(null);

export const useAuthContext = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};

// Utilitaires
export const getAuthToken = (): string | null => {
  return localStorage.getItem('finshark_token');
};

export const getAuthUser = (): User | null => {
  const userData = localStorage.getItem('finshark_user');
  return userData ? JSON.parse(userData) : null;
};

export const isAuthenticated = (): boolean => {
  return !!getAuthToken() && !!getAuthUser();
};

// Configuration des headers pour les requêtes authentifiées
export const getAuthHeaders = (): Record<string, string> => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

// Hook pour les requêtes API authentifiées
export const useAuthenticatedFetch = () => {
  const { logout } = useAuth();

  const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
    const headers = {
      ...getAuthHeaders(),
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers
    });

    // Si la réponse est 401 (non autorisé), déconnecter l'utilisateur
    if (response.status === 401) {
      logout();
      throw new Error('Session expirée. Veuillez vous reconnecter.');
    }

    return response;
  };

  return authenticatedFetch;
};
