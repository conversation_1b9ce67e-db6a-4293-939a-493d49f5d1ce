# Dockerfile pour le service Go (Auth)
FROM golang:1.24-alpine AS base

# Installer les dépendances système
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

WORKDIR /app

# Copier les fichiers de configuration Go
COPY go.mod go.sum ./

# Télécharger les dépendances
RUN go mod download

# Stage de développement
FROM base AS development
COPY . .
EXPOSE 8003
CMD ["go", "run", "main.go"]

# Stage de build
FROM base AS build

# Copier le code source
COPY . .

# Build de l'application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o auth main.go

# Stage de production
FROM alpine:3.18 AS production

# Installer les dépendances runtime
RUN apk --no-cache add ca-certificates tzdata

# Créer un utilisateur non-root
RUN addgroup -g 1001 -S go
RUN adduser -S go -u 1001

WORKDIR /root/

# Copier le binaire
COPY --from=build --chown=go:go /app/auth ./auth

USER go

EXPOSE 8003

# Variables d'environnement
ENV GIN_MODE=release

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8003/ || exit 1

CMD ["./auth"]
