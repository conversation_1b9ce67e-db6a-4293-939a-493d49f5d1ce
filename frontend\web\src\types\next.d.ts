// Déclarations de types globales pour Next.js
/// <reference types="next" />
/// <reference types="next/image-types/global" />

declare module 'next' {
  // Re-export des types principaux de Next.js
  export * from 'next/types';
  
  // Types pour les composants Next.js
  export interface NextPageProps {
    params?: Record<string, string | string[]>;
    searchParams?: Record<string, string | string[] | undefined>;
  }

  // Types pour les layouts
  export interface LayoutProps {
    children: React.ReactNode;
    params?: Record<string, string | string[]>;
  }

  // Types pour les pages d'erreur
  export interface ErrorProps {
    error: Error & { digest?: string };
    reset: () => void;
  }

  // Types pour les pages de chargement
  export interface LoadingProps {}

  // Types pour les pages non trouvées
  export interface NotFoundProps {}

  // Types pour generateMetadata
  export interface GenerateMetadataProps {
    params?: Record<string, string | string[]>;
    searchParams?: Record<string, string | string[] | undefined>;
  }

  // Types pour generateStaticParams
  export interface GenerateStaticParamsProps {
    params?: Record<string, string | string[]>;
  }

  // Types pour generateViewport
  export interface Viewport {
    width?: string | number;
    height?: string | number;
    initialScale?: number;
    minimumScale?: number;
    maximumScale?: number;
    userScalable?: boolean;
    viewportFit?: 'auto' | 'contain' | 'cover';
    interactiveWidget?: 'resizes-visual' | 'resizes-content' | 'overlays-content';
    themeColor?: string | Array<{ media?: string; color: string }>;
    colorScheme?: 'normal' | 'light' | 'dark' | 'light dark';
  }

  // Types pour les configurations de route
  export interface RouteConfig {
    runtime?: 'nodejs' | 'edge';
    preferredRegion?: string | string[];
    maxDuration?: number;
    dynamic?: 'auto' | 'force-dynamic' | 'error' | 'force-static';
    dynamicParams?: boolean;
    revalidate?: number | false;
    fetchCache?: 'auto' | 'default-cache' | 'only-cache' | 'force-cache' | 'force-no-store' | 'default-no-store' | 'only-no-store';
    experimental_ppr?: boolean;
  }
}

// Types pour les API Routes
declare module 'next/server' {
  export interface NextRequest extends Request {
    nextUrl: {
      pathname: string;
      search: string;
      searchParams: URLSearchParams;
      href: string;
      origin: string;
      protocol: string;
      host: string;
      hostname: string;
      port: string;
      hash: string;
      clone(): NextURL;
    };
    geo?: {
      city?: string;
      country?: string;
      region?: string;
      latitude?: string;
      longitude?: string;
    };
    ip?: string;
    cookies: {
      get(name: string): { name: string; value: string } | undefined;
      getAll(): Array<{ name: string; value: string }>;
      has(name: string): boolean;
      set(name: string, value: string, options?: any): void;
      delete(name: string): void;
    };
  }

  export interface NextResponse extends Response {
    cookies: {
      get(name: string): { name: string; value: string } | undefined;
      getAll(): Array<{ name: string; value: string }>;
      has(name: string): boolean;
      set(name: string, value: string, options?: any): void;
      delete(name: string): void;
    };
  }
}

// Types pour les middlewares
declare module 'next/middleware' {
  export interface MiddlewareConfig {
    matcher?: string | string[];
  }
}

// Augmentation des types React pour Next.js
declare global {
  namespace React {
    interface HTMLAttributes<T> {
      suppressHydrationWarning?: boolean;
    }
  }
}

export {};
