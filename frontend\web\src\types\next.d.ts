// Déclarations de types globales pour Next.js
/// <reference types="next" />
/// <reference types="next/image-types/global" />

declare module 'next' {
  // Re-export des types principaux de Next.js
  export * from 'next/types';
  
  // Types pour les composants Next.js
  export interface NextPageProps {
    params?: Record<string, string | string[]>;
    searchParams?: Record<string, string | string[] | undefined>;
  }

  // Types pour les layouts
  export interface LayoutProps {
    children: React.ReactNode;
    params?: Record<string, string | string[]>;
  }

  // Types pour les pages d'erreur
  export interface ErrorProps {
    error: Error & { digest?: string };
    reset: () => void;
  }

  // Types pour les pages de chargement
  export interface LoadingProps {}

  // Types pour les pages non trouvées
  export interface NotFoundProps {}

  // Types pour generateMetadata
  export interface GenerateMetadataProps {
    params?: Record<string, string | string[]>;
    searchParams?: Record<string, string | string[] | undefined>;
  }

  // Types pour generateStaticParams
  export interface GenerateStaticParamsProps {
    params?: Record<string, string | string[]>;
  }

  // Types pour generateViewport
  export interface Viewport {
    width?: string | number;
    height?: string | number;
    initialScale?: number;
    minimumScale?: number;
    maximumScale?: number;
    userScalable?: boolean;
    viewportFit?: 'auto' | 'contain' | 'cover';
    interactiveWidget?: 'resizes-visual' | 'resizes-content' | 'overlays-content';
    themeColor?: string | Array<{ media?: string; color: string }>;
    colorScheme?: 'normal' | 'light' | 'dark' | 'light dark';
  }

  // Types pour les configurations de route
  export interface RouteConfig {
    runtime?: 'nodejs' | 'edge';
    preferredRegion?: string | string[];
    maxDuration?: number;
    dynamic?: 'auto' | 'force-dynamic' | 'error' | 'force-static';
    dynamicParams?: boolean;
    revalidate?: number | false;
    fetchCache?: 'auto' | 'default-cache' | 'only-cache' | 'force-cache' | 'force-no-store' | 'default-no-store' | 'only-no-store';
    experimental_ppr?: boolean;
  }
}

// Types pour les API Routes
declare module 'next/server' {
  export interface NextRequest extends Request {
    nextUrl: {
      pathname: string;
      search: string;
      searchParams: URLSearchParams;
      href: string;
      origin: string;
      protocol: string;
      host: string;
      hostname: string;
      port: string;
      hash: string;
      clone(): NextURL;
    };
    geo?: {
      city?: string;
      country?: string;
      region?: string;
      latitude?: string;
      longitude?: string;
    };
    ip?: string;
    cookies: {
      get(name: string): { name: string; value: string } | undefined;
      getAll(): Array<{ name: string; value: string }>;
      has(name: string): boolean;
      set(name: string, value: string, options?: any): void;
      delete(name: string): void;
    };
  }

  export interface NextResponse extends Response {
    cookies: {
      get(name: string): { name: string; value: string } | undefined;
      getAll(): Array<{ name: string; value: string }>;
      has(name: string): boolean;
      set(name: string, value: string, options?: any): void;
      delete(name: string): void;
    };
  }
}

// Types pour les middlewares
declare module 'next/middleware' {
  export interface MiddlewareConfig {
    matcher?: string | string[];
  }
}

// Types pour next/image
declare module 'next/image' {
  import { ComponentProps } from 'react';

  export interface ImageProps extends Omit<ComponentProps<'img'>, 'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'> {
    src: string | import('next/image').StaticImageData;
    alt: string;
    width?: number | `${number}` | undefined;
    height?: number | `${number}` | undefined;
    fill?: boolean;
    loader?: (resolverProps: {
      src: string;
      width: number;
      quality?: number;
    }) => string;
    quality?: number | `${number}`;
    priority?: boolean;
    loading?: 'lazy' | 'eager';
    placeholder?: 'blur' | 'empty';
    blurDataURL?: string;
    unoptimized?: boolean;
    overrideSrc?: string;
    onLoadingComplete?: (result: {
      naturalWidth: number;
      naturalHeight: number;
    }) => void;
    onLoad?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
    onError?: (event: React.SyntheticEvent<HTMLImageElement, Event>) => void;
    sizes?: string;
    style?: React.CSSProperties;
  }

  export interface StaticImageData {
    src: string;
    height: number;
    width: number;
    blurDataURL?: string;
    blurWidth?: number;
    blurHeight?: number;
  }

  declare const Image: React.ForwardRefExoticComponent<
    ImageProps & React.RefAttributes<HTMLImageElement>
  >;

  export default Image;
}

// Types pour next/link
declare module 'next/link' {
  import { ComponentProps } from 'react';

  export interface LinkProps extends Omit<ComponentProps<'a'>, 'href'> {
    href: string | URL;
    as?: string | URL;
    replace?: boolean;
    scroll?: boolean;
    shallow?: boolean;
    passHref?: boolean;
    prefetch?: boolean | null;
    locale?: string | false;
    legacyBehavior?: boolean;
  }

  declare const Link: React.ForwardRefExoticComponent<
    LinkProps & React.RefAttributes<HTMLAnchorElement>
  >;

  export default Link;
}

// Types pour next/head
declare module 'next/head' {
  import { ReactNode } from 'react';

  export interface HeadProps {
    children?: ReactNode;
  }

  declare const Head: React.FC<HeadProps>;
  export default Head;
}

// Types pour next/script
declare module 'next/script' {
  import { ComponentProps } from 'react';

  export interface ScriptProps extends Omit<ComponentProps<'script'>, 'src' | 'onLoad' | 'onReady' | 'onError'> {
    src?: string;
    strategy?: 'beforeInteractive' | 'afterInteractive' | 'lazyOnload' | 'worker';
    onLoad?: (e: Event) => void;
    onReady?: () => void;
    onError?: (e: ErrorEvent) => void;
  }

  declare const Script: React.ForwardRefExoticComponent<
    ScriptProps & React.RefAttributes<HTMLScriptElement>
  >;

  export default Script;
}

// Types pour next/router
declare module 'next/router' {
  export interface NextRouter {
    route: string;
    pathname: string;
    query: { [key: string]: string | string[] | undefined };
    asPath: string;
    basePath: string;
    locale?: string;
    locales?: string[];
    defaultLocale?: string;
    domainLocales?: Array<{
      domain: string;
      defaultLocale: string;
      locales?: string[];
    }>;
    isReady: boolean;
    isPreview: boolean;
    isLocaleDomain: boolean;
    push(url: string, as?: string, options?: any): Promise<boolean>;
    replace(url: string, as?: string, options?: any): Promise<boolean>;
    reload(): void;
    back(): void;
    forward(): void;
    prefetch(url: string, asPath?: string, options?: any): Promise<void>;
    beforePopState(cb: (state: any) => boolean): void;
    events: {
      on(type: string, handler: (...args: any[]) => void): void;
      off(type: string, handler: (...args: any[]) => void): void;
      emit(type: string, ...args: any[]): void;
    };
    isFallback: boolean;
  }

  export function useRouter(): NextRouter;
  export const router: NextRouter;
  export default router;
}

// Augmentation des types React pour Next.js
declare global {
  namespace React {
    interface HTMLAttributes<T> {
      suppressHydrationWarning?: boolean;
    }
  }
}

export {};
