# Dependencies
node_modules/
*/node_modules/
**/node_modules/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
.next/
out/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Rust specific
target/
Cargo.lock

# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib

# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# TypeScript
*.tsbuildinfo

# Documentation (keeping for now)
# Cahier_des_charges.md
Cahier_des_charges.md

# Docker files
docker-compose.prod.yml
docker-compose.simple.yml
docker-compose.yml
