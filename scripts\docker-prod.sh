#!/bin/bash

# Script pour déployer en production avec Docker

echo "🚀 Déploiement en production FinShark..."

# Vérifier les variables d'environnement critiques
if [ -z "$POSTGRES_PASSWORD" ]; then
    echo "❌ POSTGRES_PASSWORD n'est pas défini"
    exit 1
fi

# Construire les images de production
echo "🔨 Construction des images de production..."
docker-compose -f docker-compose.prod.yml build --no-cache

# Arrêter les anciens conteneurs
echo "🛑 Arrêt des anciens conteneurs..."
docker-compose -f docker-compose.prod.yml down

# Lancer les nouveaux conteneurs
echo "🚀 Lancement des nouveaux conteneurs..."
docker-compose -f docker-compose.prod.yml up -d

# Attendre que les services soient prêts
echo "⏳ Attente du démarrage des services..."
sleep 30

# Vérifier l'état des services
echo "📊 État des services:"
docker-compose -f docker-compose.prod.yml ps

# Nettoyer les images inutilisées
echo "🧹 Nettoyage des images inutilisées..."
docker image prune -f

echo ""
echo "✅ Déploiement en production terminé!"
echo ""
echo "🌐 Application disponible sur:"
echo "   Frontend:      http://localhost"
echo "   API Gateway:   http://localhost/api/"
echo ""
echo "📝 Commandes utiles:"
echo "   Voir les logs:     docker-compose -f docker-compose.prod.yml logs -f"
echo "   Arrêter:          docker-compose -f docker-compose.prod.yml down"
echo "   Redémarrer:       docker-compose -f docker-compose.prod.yml restart"
