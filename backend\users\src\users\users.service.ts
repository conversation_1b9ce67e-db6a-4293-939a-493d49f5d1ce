import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UserResponseDto } from './dto/user-response.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Créer un nouveau utilisateur
   */
  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    const { username, password } = createUserDto;

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await this.userRepository.findOne({
      where: { username },
    });

    if (existingUser) {
      throw new ConflictException('Ce nom d\'utilisateur est déjà utilisé');
    }

    try {
      // Hasher le mot de passe
      const saltRounds = 12;
      const password_hash = await bcrypt.hash(password, saltRounds);

      // Créer l'utilisateur
      const user = this.userRepository.create({
        username,
        password_hash,
      });

      // Sauvegarder en base
      const savedUser = await this.userRepository.save(user);

      // Retourner la réponse sans le hash
      return new UserResponseDto(savedUser);
    } catch (error) {
      throw new BadRequestException('Erreur lors de la création de l\'utilisateur');
    }
  }

  /**
   * Trouver un utilisateur par son ID
   */
  async findOne(id: string): Promise<UserResponseDto> {
    const user = await this.userRepository.findOne({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('Utilisateur non trouvé');
    }

    return new UserResponseDto(user);
  }

  /**
   * Trouver un utilisateur par son nom d'utilisateur
   */
  async findByUsername(username: string): Promise<UserResponseDto> {
    const user = await this.userRepository.findOne({
      where: { username },
    });

    if (!user) {
      throw new NotFoundException('Utilisateur non trouvé');
    }

    return new UserResponseDto(user);
  }

  /**
   * Trouver un utilisateur avec son hash de mot de passe (pour l'authentification)
   */
  async findByUsernameWithPassword(username: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { username },
      select: ['id', 'username', 'password_hash', 'created_at', 'updated_at'],
    });
  }

  /**
   * Vérifier si un nom d'utilisateur est disponible
   */
  async isUsernameAvailable(username: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { username },
    });

    return !user;
  }

  /**
   * Obtenir tous les utilisateurs (pour l'administration)
   */
  async findAll(): Promise<UserResponseDto[]> {
    const users = await this.userRepository.find({
      order: { created_at: 'DESC' },
    });

    return users.map(user => new UserResponseDto(user));
  }

  /**
   * Supprimer un utilisateur
   */
  async remove(id: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('Utilisateur non trouvé');
    }

    await this.userRepository.remove(user);
  }
}
