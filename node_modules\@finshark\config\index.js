// Point d'entrée principal pour @finshark/config
export {
  default as eslintConfig,
  reactConfig,
  reactNativeConfig,
} from './eslint.config.js';
export { default as postcssConfig } from './postcss.config.js';
export { default as tailwindConfig } from './tailwind.config.js';
export { default as prettierConfig } from './prettier.config.js';

// Réexport des configurations pour faciliter l'utilisation
export const configs = {
  eslint: {
    base: './eslint.config.js',
    react: './eslint.config.js',
    reactNative: './eslint.config.js',
  },
  postcss: './postcss.config.js',
  tailwind: './tailwind.config.js',
  typescript: './tsconfig.base.json',
  prettier: './prettier.config.js',
};
