{"rustc": 12848521558442915983, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 18413655215056772274, "profile": 6087009849126995362, "path": 14957712348298899370, "deps": [[1903065274347263737, "tracing_attributes", false, 16889722945579414660], [11809678037142197677, "pin_project_lite", false, 5839992021248501570], [15399619262696441677, "log", false, 5676135233195032588], [18194486745044415099, "tracing_core", false, 15893325573868202219]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-5c7b491da168f3bc/dep-lib-tracing"}}], "rustflags": [], "metadata": 14951919238068079556, "config": 2202906307356721367, "compile_kind": 0}